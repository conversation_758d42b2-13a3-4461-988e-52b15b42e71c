<template>
  <div>
    <div v-if="loading">Loading outlier data...</div>
    <div v-else>
      <!-- Filters -->
           <div class="p-6 space-y-6 bg-white shadow-sm">
        <div class="flex flex-wrap items-center gap-4 mb-4">
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Group:</span>
                <Multiselect v-model="filters.group" :options="groupOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Department:</span>
                <Multiselect v-model="filters.department" :options="departmentOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Class:</span>
                <Multiselect v-model="filters.class" :options="classOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">SubClass:</span>
                <Multiselect v-model="filters.subclass" :options="subclassOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Store Code:</span>
                <Multiselect v-model="filters.storeId" :options="storeOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Outlier Type:</span>
                <Multiselect v-model="filters.outlierStatus" :options="outlierStatusOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex gap-3 ml-auto">
                <button
                    @click="applyFilters"
                    class="bg-positive text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Apply Filters
                </button>
                <button
                    @click="resetFilters"
                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Reset Filters
                </button>
                <button
                    @click="approveAll"
                    class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Approve All
                </button>
                <button
                    @click="rejectAll"
                    class="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Reject All
                </button>
                                                <button
                                    @click="openChart()"
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors"
                                >
                                    Chart
                                </button>
            </div>
        </div>
           </div>
      <!-- Table -->
        <!-- Data Table -->
        <div class="overflow-x-auto">
            <!-- No Data Message -->
            <div v-if="paginatedRows.length === 0" class="text-center py-8 text-gray-500">
                No outlier data found for the selected filters
            </div>

            <!-- Table -->
            <table v-else class="w-full text-sm border border-gray-200 rounded-lg">
                <thead class="bg-primary/50">
                <tr class="text-left text-tertiary font-semibold">
                <th class="p-3">Group</th>
                <th class="p-3">Department</th>
                <th class="p-3">Class</th>
                <th class="p-3">Subclass</th>
                <th class="p-3">Store ID</th>
                <th class="p-3">Month</th>
                <th class="p-3">LM</th>
                <th class="p-3">LM Contribution</th>
                <th class="p-3">GMV/day</th>
                <th class="p-3">Outlier Status</th>
                <th class="p-3">Suggested Value</th>
                <th class="p-3">Action</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(row, index) in paginatedRows" :key="index">
                <td class="py-2 px-4">{{ row.group }}</td>
                <td class="py-2 px-4">{{ row.department }}</td>
                <td class="py-2 px-4">{{ row.class }}</td>
                <td class="py-2 px-4">{{ row.subclass }}</td>
                <td class="py-2 px-4">{{ row.storeId }}</td>
                <td class="py-2 px-4">{{ row.month }}</td>
                <td class="py-2 px-4">{{ row.totalLm }}</td>
                <td class="py-2 px-4">{{ row.lmContrib }}</td>
                <td class="py-2 px-4">{{ row.gmvPerDay }}</td>
                <td class="py-2 px-4">
                <span :class="getOutlierStatusClass(row.outlierStatus)">
                {{ getLabelByValue(row.outlierStatus) }}
                </span>
                </td>
                <td class="py-2 px-4">{{ row.suggestedTotalLm }}</td>
                <td class="py-2 px-4">
                <!-- Show Approved/Rejected labels if status is set -->
                <div v-if="row.outlierFinalStatus === 'APPROVED'">
                <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-semibold">Approved</span>
                </div>
                <div v-else-if="row.outlierFinalStatus === 'REJECTED'">
                <span class="bg-red-100 text-red-700 px-2 py-1 rounded text-xs font-semibold">Rejected</span>
                </div>
                <!-- Show buttons only for outliers that haven't been approved/rejected -->
                <div v-else-if="isOutlier(row.outlierStatus)" class="flex gap-2">
                <button
                @click="approveRow(row)"
                :disabled="row.processing"
                class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-xs transition-colors"
                >
                {{ row.processing ? 'Processing...' : 'Approve' }}
                </button>
                <button
                @click="rejectRow(row)"
                :disabled="row.processing"
                class="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-xs transition-colors"
                >
                {{ row.processing ? 'Processing...' : 'Reject' }}
                </button>
                </div>
                <!-- Show nothing for normal items that haven't been processed -->
                </td>
                </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-between items-center mt-4">
            <div class="text-sm text-gray-600">
                Page {{ currentPage }} of {{ totalPages }} ({{ rows.length }} total rows)
            </div>
            <div class="flex gap-2">
                <button
                    @click="previousPage"
                    :disabled="currentPage === 1"
                    class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                    Previous
                </button>
                <button
                    @click="nextPage"
                    :disabled="currentPage === totalPages"
                    class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                    Next
                </button>
            </div>
        </div>

        <!-- Chart Modal -->
        <div v-if="showChartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="closeChart">
            <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto" @click.stop>
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Outlier Chart</h2>
                    <button @click="closeChart" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                </div>
                <OutlierChart  :originalRows="originalRows" />
            </div>
        </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import Multiselect from '@vueform/multiselect' // corrected import
import '@vueform/multiselect/themes/default.css' // required for styling
import OutlierChart from './OutlierChart.vue'
import axios from 'axios'

// Filters and options
const filters = ref({
    group: [],
    department: [],
    class: [],
    subclass: [],
    storeId: [],
    outlierStatus: [] // Default selected
});

// Hierarchical filter options
const groupOptions = computed(() => {
    const allGroups = [...new Set(originalRows.value.map(r => r.group))];
    return allGroups.map(g => ({ value: g, label: g }));
});

const departmentOptions = computed(() => {
    let filtered = originalRows.value;
    if (filters.value.group.length > 0) {
        filtered = filtered.filter(r => filters.value.group.includes(r.group));
    }
    const allDepartments = [...new Set(filtered.map(r => r.department))];
    return allDepartments.map(d => ({ value: d, label: d }));
});

const classOptions = computed(() => {
    let filtered = originalRows.value;
    if (filters.value.group.length > 0) {
        filtered = filtered.filter(r => filters.value.group.includes(r.group));
    }
    if (filters.value.department.length > 0) {
        filtered = filtered.filter(r => filters.value.department.includes(r.department));
    }
    const allClasses = [...new Set(filtered.map(r => r.class))];
    return allClasses.map(c => ({ value: c, label: c }));
});

const subclassOptions = computed(() => {
    let filtered = originalRows.value;
    if (filters.value.group.length > 0) {
        filtered = filtered.filter(r => filters.value.group.includes(r.group));
    }
    if (filters.value.department.length > 0) {
        filtered = filtered.filter(r => filters.value.department.includes(r.department));
    }
    if (filters.value.class.length > 0) {
        filtered = filtered.filter(r => filters.value.class.includes(r.class));
    }
    const allSubclasses = [...new Set(filtered.map(r => r.subclass))];
    return allSubclasses.map(s => ({ value: s, label: s }));
});

const storeOptions = computed(() => {
    let filtered = originalRows.value;
    if (filters.value.group.length > 0) {
        filtered = filtered.filter(r => filters.value.group.includes(r.group));
    }
    if (filters.value.department.length > 0) {
        filtered = filtered.filter(r => filters.value.department.includes(r.department));
    }
    if (filters.value.class.length > 0) {
        filtered = filtered.filter(r => filters.value.class.includes(r.class));
    }
    if (filters.value.subclass.length > 0) {
        filtered = filtered.filter(r => filters.value.subclass.includes(r.subclass));
    }
    const allStores = [...new Set(filtered.map(r => r.storeId))];
    return allStores.map(id => ({ value: id, label: id }));
});

const outlierStatusOptions = [
  { value: 'MAJOR_OUTLIER', label: 'Major Outlier' },
  { value: 'MINOR_OUTLIER', label: 'Minor Outlier' },
  { value: 'NORMAL', label: 'Normal' },
];

// Data
const rows = ref([]);
const originalRows = ref([]); // Keep original data separate
const loading = ref(false);
const error = ref(null);
// Pagination
const currentPage = ref(1);
const pageSize = 10;

// Chart Modal
const showChartModal = ref(false);
const selectedRow = ref(null);

// Computed properties for pagination
const totalPages = computed(() => Math.ceil(rows.value.length / pageSize));
const paginatedRows = computed(() => {
    const start = (currentPage.value - 1) * pageSize;
    const end = start + pageSize;
    return rows.value.slice(start, end);
});
const fetchOutlierData = async () => {
    loading.value = true;
    error.value = null;
    
    try {
        const response = await axios.get('scenario/outlier/');

        const mappedData = response.data.data.map(item => ({
            group: item.grp_nm,
            department: item.dpt_nm,
            class: item.clss_nm,
            subclass: item.sub_clss_nm,
            storeId: item.loc_cd,
            month: item.month,
            totalLm: item.total_lm,
            lmContrib: `${item.lm_contribution_in_store}%`,
            gmvPerDay: item.gmv_per_day,
            outlierStatus: item.outlier_status || 'NORMAL',
            outlierFinalStatus: item.outlier_status_final,
            suggestedTotalLm: item.suggested_total_lm,
            action: 'APPROVE', // Default action
            processing: false // For individual row loading states
        }));

        // Store original data
        originalRows.value = mappedData;

        // Show all data initially (no filters applied)
        rows.value = mappedData;

    } catch (err) {
        console.error('Error fetching outlier data:', err);
        error.value = err.response?.data?.message || err.message || 'Failed to load outlier data';
    } finally {
        loading.value = false;
    }
}

const isOutlier = (status) => ["MAJOR_OUTLIER", "MINOR_OUTLIER"].includes(status);

const getOutlierStatusClass = (status) => {
    switch (status) {
        case "NORMAL": return "bg-positive text-white px-2 py-1 rounded text-xs";
        case "MINOR_OUTLIER": return "bg-neutral text-white px-2 py-1 rounded text-xs";
        case "MAJOR_OUTLIER": return "bg-negative text-white px-2 py-1 rounded text-xs";
        default: return "bg-gray-500 text-white px-2 py-1 rounded text-xs";
    }
}

// Filter functions
const applyFilters = () => {
    currentPage.value = 1;

    // Always filter from original data to avoid destructive filtering
    let filtered = [...originalRows.value];

    // Group filter
    if (filters.value.group.length > 0) {
        filtered = filtered.filter(row => filters.value.group.includes(row.group));
    }
    // Department filter
    if (filters.value.department.length > 0) {
        filtered = filtered.filter(row => filters.value.department.includes(row.department));
    }
    // Class filter
    if (filters.value.class.length > 0) {
        filtered = filtered.filter(row => filters.value.class.includes(row.class));
    }
    // Subclass filter
    if (filters.value.subclass.length > 0) {
        filtered = filtered.filter(row => filters.value.subclass.includes(row.subclass));
    }
    // Store filter
    if (filters.value.storeId.length > 0) {
        filtered = filtered.filter(row => filters.value.storeId.includes(row.storeId));
    }
    // Outlier status filter
    if (filters.value.outlierStatus.length > 0) {
        filtered = filtered.filter(row => filters.value.outlierStatus.includes(row.outlierStatus));
    }

    rows.value = filtered;
}

const resetFilters = () => {
    filters.value.group = [];
    filters.value.department = [];
    filters.value.class = [];
    filters.value.subclass = [];
    filters.value.storeId = [];
    filters.value.outlierStatus = [];
    applyFilters(); // Apply filters after reset (don't refetch data)
}

// Helper functions for "Select All" and "Clear" functionality
const selectAllSubclasses = () => {
    filters.value.subclass = subclassOptions.value.map(opt => opt.value);
}

const clearSubclasses = () => {
    filters.value.subclass = [];
}

const selectAllStores = () => {
    filters.value.storeId = storeOptions.value.map(opt => opt.value);
}

const clearStores = () => {
    filters.value.storeId = [];
}

const selectAllOutliers = () => {
    filters.value.outlierStatus = outlierStatusOptions.map(opt => opt.value);
}

const clearOutliers = () => {
    filters.value.outlierStatus = [];
}

const getLabelByValue = (value) => {
  const option = outlierStatusOptions.find(opt => opt.value === value);
  return option ? option.label : null;
}

// Pagination functions
const nextPage = () => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++;
    }
}

const previousPage = () => {
    if (currentPage.value > 1) {
        currentPage.value--;
    }
}

// API functions for approve/reject
const updateOutlierStatus = async (rowsToUpdate, finalStatus) => {
    try {
        // Always work with an array
        const rowsArray = Array.isArray(rowsToUpdate) ? rowsToUpdate : [rowsToUpdate];
        const payload = rowsArray.map(row => ({
            sub_clss_nm: row.subclass,
            loc_cd: row.storeId,
            outlier_status: row.outlierStatus,
            outlier_status_final: finalStatus,
            month: row.month
        }));

        const response = await axios.patch('/scenario/outlier/', payload);

        if (response.status === 200) {
            // Update the row status locally
            rowsArray.forEach(row => {
                row.outlierFinalStatus = finalStatus;
            });
            return true;
        }
    } catch (err) {
        console.error('Error updating outlier status:', err);
        error.value = err.response?.data?.message || err.message || 'Failed to update status';
        return false;
    }
}

const getAllOutliers = () => {
    return originalRows.value.filter(row => isOutlier(row.outlierStatus));
}

const approveRow = async (row) => {
    row.processing = true;
    const success = await updateOutlierStatus([row], 'APPROVED');
    if (success) {
        row.outlierFinalStatus = 'APPROVED';
        // Also update the original data to maintain consistency
        const originalRow = originalRows.value.find(r =>
            r.subclass === row.subclass &&
            r.storeId === row.storeId &&
            r.month === row.month
        );
        if (originalRow) {
            originalRow.outlierFinalStatus = 'APPROVED';
        }
    }
    row.processing = false;
}

const rejectRow = async (row) => {
    row.processing = true;
    const success = await updateOutlierStatus([row], 'REJECTED');
    if (success) {
        row.outlierFinalStatus = 'REJECTED';
        // Also update the original data to maintain consistency
        const originalRow = originalRows.value.find(r =>
            r.subclass === row.subclass &&
            r.storeId === row.storeId &&
            r.month === row.month
        );
        if (originalRow) {
            originalRow.outlierFinalStatus = 'REJECTED';
        }
    }
    row.processing = false;
}

// Bulk approve all outliers in current filtered view
const approveAll = async () => {
    const outlierRows = rows.value.filter(row =>
        isOutlier(row.outlierStatus) &&
        !row.outlierFinalStatus
    );
    if (outlierRows.length === 0) return;
    // Set processing state
    outlierRows.forEach(row => row.processing = true);
    const success = await updateOutlierStatus(outlierRows, 'APPROVED');
    if (success) {
        outlierRows.forEach(row => {
            row.outlierFinalStatus = 'APPROVED';
            // Also update the original data to maintain consistency
            const originalRow = originalRows.value.find(r =>
                r.subclass === row.subclass &&
                r.storeId === row.storeId &&
                r.month === row.month
            );
            if (originalRow) {
                originalRow.outlierFinalStatus = 'APPROVED';
            }
        });
    }
    outlierRows.forEach(row => row.processing = false);
}

// Bulk reject all outliers in current filtered view
const rejectAll = async () => {
    const outlierRows = rows.value.filter(row =>
        isOutlier(row.outlierStatus) &&
        !row.outlierFinalStatus
    );
    if (outlierRows.length === 0) return;
    // Set processing state
    outlierRows.forEach(row => row.processing = true);
    const success = await updateOutlierStatus(outlierRows, 'REJECTED');
    if (success) {
        outlierRows.forEach(row => {
            row.outlierFinalStatus = 'REJECTED';
            // Also update the original data to maintain consistency
            const originalRow = originalRows.value.find(r =>
                r.subclass === row.subclass &&
                r.storeId === row.storeId &&
                r.month === row.month
            );
            if (originalRow) {
                originalRow.outlierFinalStatus = 'REJECTED';
            }
        });
    }
    outlierRows.forEach(row => row.processing = false);
}

// Chart Modal Functions
const openChart = () => {
    showChartModal.value = true;
}

const closeChart = () => {
    showChartModal.value = false;
}

onMounted(() => {
    fetchOutlierData();
});
</script>
