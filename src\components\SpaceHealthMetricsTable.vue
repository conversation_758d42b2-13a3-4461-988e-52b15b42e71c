<template>
  <div class="space-y-6">
    <!-- Filters -->

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">Loading data...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <p class="text-red-600">{{ error }}</p>
    </div>

    <!-- Table -->
    <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="overflow-x-auto">
        <table class="w-full text-sm">
          <!-- Header -->
          <thead class="bg-primary sticky top-0 z-50">
            <!-- Main header groups -->
            <tr>
              <th v-for="(header, idx) in stickyHeaders" :key="`sticky-${idx}`"
                  :class="stickyHeaderClass(idx)"
                  :style="stickyHeaderStyle(idx)"
                  rowspan="2">
                {{ header }}
              </th>
              <th colspan="8" class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider border-r border-gray-300">
                Area Metrics
              </th>
              <th colspan="4" class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider border-r border-gray-300">
                Stock Metrics
              </th>
              <th colspan="4" class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider border-r border-gray-300">
                Sales Metrics
              </th>
              <th colspan="8" class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider border-r border-gray-300">
                Area Productivity
              </th>
              <th colspan="3" class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider">
                Inventory Health
              </th>
            </tr>
            <!-- Sub headers -->
            <tr>
              <th v-for="header in areaMetricsHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in stockMetricsHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in salesMetricsHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in areaProductivityHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in inventoryHealthHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
            </tr>
          </thead>

          <!-- Body -->
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(row, index) in paginatedRows" :key="index" class="hover:bg-gray-50">
              <!-- Sticky columns -->
              <td v-for="(field, idx) in stickyFields" :key="`sticky-${idx}`"
                  :class="stickyCellClass(idx)"
                  :style="stickyCellStyle(idx)">
                {{ row[field] }}
              </td>
              
              <!-- Area Metrics -->
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.lm) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.sqft) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ row.lmRank }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ row.sqftRank }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.fixtureDensity, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.lmCont, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.sqftCont, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.diff, 2) }}</td>
              
              <!-- Stock Metrics -->
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.optionCount) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.optionDensity, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.sohQty) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.stockDensity, 2) }}</td>
              
              <!-- Sales Metrics -->
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revenue) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmv) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revenuePerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmvPerDay) }}</td>
              
              <!-- Area Productivity -->
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revPerLmPerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revPerSqftPerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmvPerLmPerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmvPerSqftPerDay) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ row.revPerLmPerDayRank }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ row.revPerSqftPerDayRank }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ row.gmvPerLmPerDayRank }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ row.gmvPerSqftPerDayRank }}</td>
              
              <!-- Inventory Health -->
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.latestStock) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.currentRos, 2) }}</td>
              <td class="py-2 px-3 text-center">{{ row.coverDays }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-between items-center p-4 border-t border-gray-200">
        <div class="text-sm text-gray-600">
          Page {{ currentPage }} of {{ totalPages }} ({{ filteredRows.length }} total rows)
        </div>
        <div class="flex gap-2">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
          >
            Previous
          </button>
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import Multiselect from '@vueform/multiselect'
import '@vueform/multiselect/themes/default.css'
import axios from 'axios'

// Data and state
const loading = ref(false)
const error = ref(null)
const originalRows = ref([])
const filteredRows = ref([])
const currentPage = ref(1)
const pageSize = 10

// Filters
const filters = ref({
  storeId: [],
  group: [],
  department: [],
  class: [],
  subClass: []
})

// Filter options (will be populated from data)
const filterOptions = ref({
  storeId: {
    label: 'Store ID',
    options: [],
    placeholder: 'Select Store IDs'
  },
  group: {
    label: 'Group',
    options: [],
    placeholder: 'Select Groups'
  },
  department: {
    label: 'Department',
    options: [],
    placeholder: 'Select Departments'
  },
  class: {
    label: 'Class',
    options: [],
    placeholder: 'Select Classes'
  },
  subClass: {
    label: 'Sub Class',
    options: [],
    placeholder: 'Select Sub Classes'
  }
})

// Table structure
const stickyHeaders = ['Store ID', 'Group', 'Department', 'Class', 'Sub Class']
const stickyFields = ['storeId', 'group', 'department', 'class', 'subClass']
const columnWidths = [130, 120, 150, 120, 120] // Widths for sticky columns

const areaMetricsHeaders = ['LM', 'Sqft', 'LM Rank', 'Sqft Rank', 'Fixture Density', 'LM Cont', 'SQFT Cont', 'Diff']
const stockMetricsHeaders = ['Option Count', 'Option Density', 'SOH Qty', 'Stock Density']
const salesMetricsHeaders = ['Revenue', 'GMV', 'Revenue per day', 'GMV per day']
const areaProductivityHeaders = ['Rev per LM per day', 'Rev per Sqft per day', 'GMV per LM per day', 'GMV per Sqft per day', 'Rev per LM per day (Rank)', 'Rev per Sqft per day (Rank)', 'GMV per LM per day (Rank)', 'GMV per Sqft per day (Rank)']
const inventoryHealthHeaders = ['Latest Stock', 'Current ROS', 'Cover (days)']

// Computed properties
const stickyLeftPositions = computed(() => {
  const positions = [0]
  for (let i = 1; i < columnWidths.length; i++) {
    positions.push(positions[i - 1] + columnWidths[i - 1])
  }
  return positions
})

const totalPages = computed(() => Math.ceil(filteredRows.value.length / pageSize))

const paginatedRows = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredRows.value.slice(start, end)
})

// Methods
const stickyHeaderClass = (idx) => [
  'py-2 px-3 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 sticky z-40',
  idx === stickyHeaders.length - 1 ? 'border-r-2 border-gray-400' : ''
]

const stickyHeaderStyle = (idx) => ({
  left: stickyLeftPositions.value[idx] + 'px',
  minWidth: columnWidths[idx] + 'px'
})

const stickyCellClass = (idx) => [
  'py-2 px-3 text-center bg-white border-r border-gray-200 sticky z-30',
  idx === stickyFields.length - 1 ? 'border-r-2 border-gray-400' : ''
]

const stickyCellStyle = (idx) => ({
  left: stickyLeftPositions.value[idx] + 'px',
  minWidth: columnWidths[idx] + 'px'
})

const formatNumber = (value, decimals = 0) => {
  if (value == null || value === '') return '-'
  return Number(value).toLocaleString(undefined, { 
    minimumFractionDigits: decimals, 
    maximumFractionDigits: decimals 
  })
}

const formatCurrency = (value) => {
  if (value == null || value === '') return '-'
  return new Intl.NumberFormat('en-AE', { 
    style: 'currency', 
    currency: 'AED',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Pagination
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

// Filtering
const applyFilters = () => {
  currentPage.value = 1
  
  let filtered = [...originalRows.value]
  
  // Apply each filter
  Object.keys(filters.value).forEach(key => {
    if (filters.value[key].length > 0) {
      filtered = filtered.filter(row => 
        filters.value[key].some(filterValue => 
          row[key] === filterValue.value || row[key] === filterValue
        )
      )
    }
  })
  
  filteredRows.value = filtered
}

const clearFilters = () => {
  Object.keys(filters.value).forEach(key => {
    filters.value[key] = []
  })
  filteredRows.value = [...originalRows.value]
  currentPage.value = 1
}

// Data loading
const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // For now, generate mock data - replace with actual API call
    // TODO: Replace with actual API call like:
    // const response = await axios.get('/api/space-health-metrics/')
    // const mockData = response.data.data

    const mockData = generateMockData()
    originalRows.value = mockData
    filteredRows.value = [...mockData]

    // Populate filter options
    populateFilterOptions(mockData)

  } catch (err) {
    error.value = 'Failed to load data: ' + err.message
  } finally {
    loading.value = false
  }
}

const populateFilterOptions = (data) => {
  Object.keys(filterOptions.value).forEach(key => {
    const field = key === 'subClass' ? 'subClass' : key
    const uniqueValues = [...new Set(data.map(row => row[field]))]
    filterOptions.value[key].options = uniqueValues.map(value => ({
      value,
      label: value
    }))
  })
}

const generateMockData = () => {
  // Generate mock data based on the interface
  const stores = ['S001', 'S002', 'S003', 'S004', 'S005']
  const groups = ['Fashion', 'Home', 'Electronics']
  const departments = ['Bags', 'Home Decor', 'Electronics']
  const classes = ['Class A', 'Class B', 'Class C']
  const subClasses = ['Sub A', 'Sub B', 'Sub C']
  
  const data = []
  
  for (let i = 0; i < 50; i++) {
    data.push({
      storeId: stores[Math.floor(Math.random() * stores.length)],
      group: groups[Math.floor(Math.random() * groups.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      class: classes[Math.floor(Math.random() * classes.length)],
      subClass: subClasses[Math.floor(Math.random() * subClasses.length)],
      
      // Area Metrics
      lm: Math.floor(Math.random() * 200) + 50,
      sqft: Math.floor(Math.random() * 1000) + 200,
      lmRank: Math.floor(Math.random() * 50) + 1,
      sqftRank: Math.floor(Math.random() * 50) + 1,
      fixtureDensity: Math.random() * 5 + 1,
      lmCont: Math.random() * 100,
      sqftCont: Math.random() * 100,
      diff: Math.random() * 20 - 10,
      
      // Stock Metrics
      optionCount: Math.floor(Math.random() * 500) + 100,
      optionDensity: Math.random() * 10 + 1,
      sohQty: Math.floor(Math.random() * 1000) + 200,
      stockDensity: Math.random() * 5 + 1,
      
      // Sales Metrics
      revenue: Math.floor(Math.random() * 100000) + 10000,
      gmv: Math.floor(Math.random() * 120000) + 12000,
      revenuePerDay: Math.floor(Math.random() * 3000) + 300,
      gmvPerDay: Math.floor(Math.random() * 3600) + 360,
      
      // Area Productivity
      revPerLmPerDay: Math.random() * 100 + 10,
      revPerSqftPerDay: Math.random() * 50 + 5,
      gmvPerLmPerDay: Math.random() * 120 + 12,
      gmvPerSqftPerDay: Math.random() * 60 + 6,
      revPerLmPerDayRank: Math.floor(Math.random() * 50) + 1,
      revPerSqftPerDayRank: Math.floor(Math.random() * 50) + 1,
      gmvPerLmPerDayRank: Math.floor(Math.random() * 50) + 1,
      gmvPerSqftPerDayRank: Math.floor(Math.random() * 50) + 1,
      
      // Inventory Health
      latestStock: Math.floor(Math.random() * 2000) + 500,
      currentRos: Math.random() * 3 + 0.5,
      coverDays: Math.floor(Math.random() * 60) + 10
    })
  }
  
  return data
}

// Expose methods to parent component
defineExpose({
  loadData,
  applyFilters,
  clearFilters
})

// Emit events
const emit = defineEmits(['loading'])

// Update loading function to emit events
const loadDataWithEmit = async () => {
  emit('loading', true)
  await loadData()
  emit('loading', false)
}

// Lifecycle
onMounted(() => {
  loadDataWithEmit()
})

// Watch for filter changes to enable dynamic filtering
watch(filters, () => {
  applyFilters()
}, { deep: true })
</script>

<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure sticky columns stay on top */
.sticky {
  position: sticky;
}

/* Multiselect styling */
.multiselect {
  min-height: 40px;
}
</style>
